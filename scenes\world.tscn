[gd_scene load_steps=79 format=4 uid="uid://dq0nxjiipwin0"]

[ext_resource type="Texture2D" uid="uid://dby826kpimo7m" path="res://resources/MapLayers/MAP_SPEED_MODIFIER.png" id="2_4wyf3"]
[ext_resource type="PackedScene" uid="uid://io5tjlaupxaq" path="res://scenes/player.tscn" id="2_rwgxs"]
[ext_resource type="PackedScene" uid="uid://bqxm8y4n5k2vw" path="res://scenes/enemies/EnemyTerritory.tscn" id="2_territory"]
[ext_resource type="Texture2D" uid="uid://bihweui2tqxlx" path="res://resources/solaria/exterior/Water Edges 02-Sheet.png" id="2_ts3gi"]
[ext_resource type="Texture2D" uid="uid://fwd3kpqnwx73" path="res://resources/solaria/exterior/Waves 01-Sheet.png" id="3_0nbtd"]
[ext_resource type="Texture2D" uid="uid://bsrmiu50aadu8" path="res://resources/solaria/buildings/bridge.png" id="3_7t5mc"]
[ext_resource type="Texture2D" uid="uid://bt5khgw61bsyn" path="res://resources/solaria/exterior/terrain_global_region_2.png" id="3_817gm"]
[ext_resource type="PackedScene" uid="uid://bvn8x70a01a2b" path="res://scenes/CustomDataLayerManager.tscn" id="3_k0juu"]
[ext_resource type="PackedScene" uid="uid://cw5lj8bgfsyaj" path="res://scenes/DroppedResourceManager.tscn" id="4_dropped_resource_manager"]
[ext_resource type="Texture2D" uid="uid://cu5mp0k42owf6" path="res://resources/MapLayers/MAP_CAN_BUILDING.png" id="5_ctatt"]
[ext_resource type="Texture2D" uid="uid://uo7h5ut4grwa" path="res://resources/MapLayers/CAN_DESTROYABLE_OBJECT.png" id="5_qfnet"]
[ext_resource type="Texture2D" uid="uid://cd4enhwjmg2rh" path="res://resources/MapLayers/MAP_REGIONS.png" id="6_i7141"]
[ext_resource type="Texture2D" uid="uid://ci87nvy1i2taf" path="res://resources/MapLayers/MAP_CAN_BRIDGE.png" id="6_lakw3"]
[ext_resource type="PackedScene" uid="uid://cxvn8ywqxhqy" path="res://scenes/regions/Region1Manager.tscn" id="7_7r4gi"]
[ext_resource type="PackedScene" path="res://scenes/regions/Region2Manager.tscn" id="7_region2"]
[ext_resource type="PackedScene" path="res://scenes/regions/Region3Manager.tscn" id="7_region3"]
[ext_resource type="PackedScene" path="res://scenes/regions/Region4Manager.tscn" id="7_region4"]
[ext_resource type="PackedScene" uid="uid://vbvi5dvvi4tk" path="res://scenes/regions/Region5Manager.tscn" id="7_region5"]
[ext_resource type="PackedScene" path="res://scenes/regions/Region7Manager.tscn" id="7_region7"]
[ext_resource type="PackedScene" uid="uid://bxvn8ywqxhqy8" path="res://scenes/regions/Region8Manager.tscn" id="7_region8"]
[ext_resource type="PackedScene" uid="uid://doox47tk4ct41" path="res://scenes/mapObjects/BerryBush.tscn" id="8_dss4m"]
[ext_resource type="PackedScene" uid="uid://c13q3mm3n1etg" path="res://scenes/UI/inventory/SelectedToolPanel.tscn" id="9_7r4gi"]
[ext_resource type="PackedScene" uid="uid://dumldq4wvv7y2" path="res://scenes/mapObjects/Rock2.tscn" id="9_7t5mc"]
[ext_resource type="Texture2D" uid="uid://862pvw3ao7mr" path="res://resources/MapLayers/CAN_ENEMY.png" id="9_8u370"]
[ext_resource type="PackedScene" uid="uid://c7pcpuwwc2e7k" path="res://scenes/UI/inventory/InventoryMenu.tscn" id="10_w7kh3"]
[ext_resource type="PackedScene" uid="uid://bmvk4ryge08cs" path="res://scenes/BuildingPlacer.tscn" id="11_buildingplacer"]
[ext_resource type="PackedScene" uid="uid://bnxwvx42a6onm" path="res://scenes/UI/PlayerStatusPanel.tscn" id="11_jhx03"]
[ext_resource type="PackedScene" uid="uid://cmwb7f5np1s7s" path="res://scenes/BuildingManager.tscn" id="12_buildingmanager"]
[ext_resource type="PackedScene" uid="uid://onk0i1nfb3v1" path="res://scenes/ChestManager.tscn" id="13_chestmanager"]
[ext_resource type="PackedScene" path="res://scenes/PlantingManager.tscn" id="13_plantingmanager"]
[ext_resource type="PackedScene" uid="uid://cunpkoexg6ab7" path="res://scenes/mapObjects/animals/Rabbit.tscn" id="14_o8fc1"]
[ext_resource type="PackedScene" uid="uid://c0ux6u1x153iu" path="res://scenes/mapObjects/BigGreenBush.tscn" id="19_1jxku"]
[ext_resource type="PackedScene" uid="uid://2duciswm4agd" path="res://scenes/mapObjects/planting/plant.tscn" id="19_f17e3"]
[ext_resource type="Texture2D" uid="uid://cjjtrrpm57183" path="res://resources/solaria/exterior/regionLockedForground.png" id="19_pm21f"]
[ext_resource type="PackedScene" uid="uid://dd328ucgp21ei" path="res://scenes/npcs/tutorialNPC.tscn" id="20_2lf6f"]
[ext_resource type="PackedScene" uid="uid://cp77rcdn3hwcd" path="res://scenes/mapObjects/planting/SeedMove.tscn" id="20_x2olw"]
[ext_resource type="Script" uid="uid://c2vu6ohykhe8q" path="res://scenes/regions/RegionBlocker.cs" id="21_oo54l"]
[ext_resource type="Script" uid="uid://dqdmcmkk138si" path="res://scenes/RegionUnlockManager.cs" id="22_regionunlock"]
[ext_resource type="PackedScene" uid="uid://c0yna3biwkpso" path="res://scenes/mapObjects/Tree2.tscn" id="24_eo1xk"]
[ext_resource type="PackedScene" uid="uid://c1bfwgt87ed2l" path="res://scenes/mapObjects/CopperRock.tscn" id="25_18clu"]
[ext_resource type="PackedScene" uid="uid://rvxuixamqy64" path="res://scenes/mapObjects/Rock.tscn" id="25_eo1xk"]
[ext_resource type="PackedScene" uid="uid://cjr3burcalm2m" path="res://scenes/mapObjects/GreenBush2.tscn" id="26_4wdv8"]
[ext_resource type="PackedScene" uid="uid://do38ypjkmgxt4" path="res://scenes/mapObjects/GreenBush.tscn" id="26_18clu"]
[ext_resource type="PackedScene" uid="uid://f0keu4ntu4x0" path="res://scenes/mapObjects/BrownMushroom.tscn" id="27_d46x8"]
[ext_resource type="PackedScene" uid="uid://b4fxg8m50ebig" path="res://scenes/mapObjects/buildings/Grindstone.tscn" id="27_x2olw"]
[ext_resource type="PackedScene" uid="uid://jglbfpyl6t71" path="res://scenes/UI/mapPanel/MapPanel.tscn" id="28_0nbtd"]
[ext_resource type="PackedScene" uid="uid://j6jd2tdkx7qu" path="res://scenes/mapObjects/shop/Shop.tscn" id="28_37qwj"]
[ext_resource type="PackedScene" uid="uid://dxpy46omsop1" path="res://scenes/regions/Region6Manager.tscn" id="28_d46x8"]
[ext_resource type="PackedScene" uid="uid://7kuppoo036sw" path="res://scenes/UI/dayNight/DayNight.tscn" id="32_fh13f"]
[ext_resource type="PackedScene" uid="uid://kjpoq0o6wb7" path="res://scenes/regions/region2/QuestBoard.tscn" id="32_rup4s"]
[ext_resource type="PackedScene" uid="uid://dna20kwegwn3d" path="res://scenes/regions/region7/DungeonEntrance.tscn" id="41_4wdv8"]
[ext_resource type="PackedScene" uid="uid://c2e258d7v80qf" path="res://scenes/regions/region6/Region6QuestSystem.tscn" id="41_18clu"]
[ext_resource type="PackedScene" uid="uid://cbgclp7xhqtsd" path="res://scenes/mapObjects/houseMap3/HouseExterior.tscn" id="43_eo1xk"]
[ext_resource type="PackedScene" uid="uid://dq3424ky4siwr" path="res://scenes/regions/region8/EnemySpawnRegion8.tscn" id="49_e68xj"]

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_0nbtd"]
texture = ExtResource("2_ts3gi")
0:0/animation_separation = Vector2i(2, 0)
0:0/animation_speed = 4.0
0:0/animation_frame_0/duration = 1.0
0:0/animation_frame_1/duration = 1.0
0:0/animation_frame_2/duration = 1.0
0:0/animation_frame_3/duration = 1.0
0:0/0 = 0
0:0/0/terrain_set = 0
0:0/0/terrain = 0
0:0/0/terrains_peering_bit/bottom_right_corner = 0
1:0/animation_separation = Vector2i(2, 1)
1:0/animation_speed = 4.0
1:0/animation_frame_0/duration = 1.0
1:0/animation_frame_1/duration = 1.0
1:0/animation_frame_2/duration = 1.0
1:0/animation_frame_3/duration = 1.0
1:0/0 = 0
1:0/0/terrain_set = 0
1:0/0/terrain = 0
1:0/0/terrains_peering_bit/bottom_right_corner = 0
1:0/0/terrains_peering_bit/bottom_left_corner = 0
0:1/animation_separation = Vector2i(2, 0)
0:1/animation_speed = 4.0
0:1/animation_frame_0/duration = 1.0
0:1/animation_frame_1/duration = 1.0
0:1/animation_frame_2/duration = 1.0
0:1/animation_frame_3/duration = 1.0
0:1/0 = 0
0:1/0/terrain_set = 0
0:1/0/terrain = 0
0:1/0/terrains_peering_bit/bottom_right_corner = 0
0:1/0/terrains_peering_bit/top_right_corner = 0
0:2/animation_separation = Vector2i(2, 0)
0:2/animation_speed = 4.0
0:2/animation_frame_0/duration = 1.0
0:2/animation_frame_1/duration = 1.0
0:2/animation_frame_2/duration = 1.0
0:2/animation_frame_3/duration = 1.0
0:2/0 = 0
0:2/0/terrain_set = 0
0:2/0/terrain = 0
0:2/0/terrains_peering_bit/top_right_corner = 0
1:2/animation_separation = Vector2i(2, 0)
1:2/animation_speed = 4.0
1:2/animation_frame_0/duration = 1.0
1:2/animation_frame_1/duration = 1.0
1:2/animation_frame_2/duration = 1.0
1:2/animation_frame_3/duration = 1.0
1:2/0 = 0
1:2/0/terrain_set = 0
1:2/0/terrain = 0
1:2/0/terrains_peering_bit/top_left_corner = 0
1:2/0/terrains_peering_bit/top_right_corner = 0
2:2/animation_separation = Vector2i(2, 0)
2:2/animation_speed = 4.0
2:2/animation_frame_0/duration = 1.0
2:2/animation_frame_1/duration = 1.0
2:2/animation_frame_2/duration = 1.0
2:2/animation_frame_3/duration = 1.0
2:2/0 = 0
2:2/0/terrain_set = 0
2:2/0/terrain = 0
2:2/0/terrains_peering_bit/top_left_corner = 0
2:1/animation_separation = Vector2i(2, 0)
2:1/animation_speed = 4.0
2:1/animation_frame_0/duration = 1.0
2:1/animation_frame_1/duration = 1.0
2:1/animation_frame_2/duration = 1.0
2:1/animation_frame_3/duration = 1.0
2:1/0 = 0
2:1/0/terrain_set = 0
2:1/0/terrain = 0
2:1/0/terrains_peering_bit/bottom_left_corner = 0
2:1/0/terrains_peering_bit/top_left_corner = 0
2:0/animation_separation = Vector2i(2, 0)
2:0/animation_speed = 4.0
2:0/animation_frame_0/duration = 1.0
2:0/animation_frame_1/duration = 1.0
2:0/animation_frame_2/duration = 1.0
2:0/animation_frame_3/duration = 1.0
2:0/0 = 0
2:0/0/terrain_set = 0
2:0/0/terrain = 0
2:0/0/terrains_peering_bit/bottom_left_corner = 0
1:1/0 = 0
1:1/0/terrain_set = 0
1:1/0/terrain = 0
1:1/0/terrains_peering_bit/bottom_right_corner = 0
1:1/0/terrains_peering_bit/bottom_left_corner = 0
1:1/0/terrains_peering_bit/top_left_corner = 0
1:1/0/terrains_peering_bit/top_right_corner = 0
1:1/0/custom_data_0 = 1
0:6/0 = 0
0:6/0/terrain_set = 0
4:1/0 = 0
4:1/0/custom_data_0 = 2
7:1/0 = 0
7:1/0/custom_data_0 = 3
10:1/0 = 0
10:1/0/custom_data_0 = 4
0:3/animation_separation = Vector2i(1, 0)
0:3/animation_speed = 4.0
0:3/animation_frame_0/duration = 1.0
0:3/animation_frame_1/duration = 1.0
0:3/animation_frame_2/duration = 1.0
0:3/animation_frame_3/duration = 1.0
0:3/0 = 0
0:3/0/terrain_set = 0
0:3/0/terrain = 0
0:3/0/terrains_peering_bit/bottom_left_corner = 0
0:3/0/terrains_peering_bit/top_left_corner = 0
0:3/0/terrains_peering_bit/top_right_corner = 0
1:3/animation_separation = Vector2i(1, 0)
1:3/animation_speed = 4.0
1:3/animation_frame_0/duration = 1.0
1:3/animation_frame_1/duration = 1.0
1:3/animation_frame_2/duration = 1.0
1:3/animation_frame_3/duration = 1.0
1:3/0 = 0
1:3/0/terrain_set = 0
1:3/0/terrain = 0
1:3/0/terrains_peering_bit/bottom_right_corner = 0
1:3/0/terrains_peering_bit/top_left_corner = 0
1:3/0/terrains_peering_bit/top_right_corner = 0
1:4/animation_separation = Vector2i(1, 0)
1:4/animation_speed = 4.0
1:4/animation_frame_0/duration = 1.0
1:4/animation_frame_1/duration = 1.0
1:4/animation_frame_2/duration = 1.0
1:4/animation_frame_3/duration = 1.0
1:4/0 = 0
1:4/0/terrain_set = 0
1:4/0/terrain = 0
1:4/0/terrains_peering_bit/bottom_right_corner = 0
1:4/0/terrains_peering_bit/bottom_left_corner = 0
1:4/0/terrains_peering_bit/top_right_corner = 0
0:4/animation_separation = Vector2i(1, 0)
0:4/animation_speed = 4.0
0:4/animation_frame_0/duration = 1.0
0:4/animation_frame_1/duration = 1.0
0:4/animation_frame_2/duration = 1.0
0:4/animation_frame_3/duration = 1.0
0:4/0 = 0
0:4/0/terrain_set = 0
0:4/0/terrain = 0
0:4/0/terrains_peering_bit/bottom_right_corner = 0
0:4/0/terrains_peering_bit/bottom_left_corner = 0
0:4/0/terrains_peering_bit/top_left_corner = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_37qwj"]
texture = ExtResource("3_0nbtd")
0:0/animation_speed = 4.0
0:0/animation_frame_0/duration = 1.0
0:0/animation_frame_1/duration = 1.0
0:0/animation_frame_2/duration = 1.0
0:0/animation_frame_3/duration = 1.0
0:0/0 = 0
0:1/animation_speed = 4.0
0:1/animation_frame_0/duration = 1.0
0:1/animation_frame_1/duration = 1.0
0:1/animation_frame_2/duration = 1.0
0:1/animation_frame_3/duration = 1.0
0:1/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_ggc52"]
texture = ExtResource("3_817gm")
0:0/animation_separation = Vector2i(2, 0)
0:0/animation_speed = 4.0
0:0/animation_frame_0/duration = 1.0
0:0/animation_frame_1/duration = 1.0
0:0/animation_frame_2/duration = 1.0
0:0/animation_frame_3/duration = 1.0
0:0/0 = 0
0:0/0/terrain_set = 0
0:0/0/terrain = 1
0:0/0/terrains_peering_bit/bottom_right_corner = 1
1:0/animation_separation = Vector2i(2, 0)
1:0/animation_speed = 4.0
1:0/animation_frame_0/duration = 1.0
1:0/animation_frame_1/duration = 1.0
1:0/animation_frame_2/duration = 1.0
1:0/animation_frame_3/duration = 1.0
1:0/0 = 0
1:0/0/terrain_set = 0
1:0/0/terrain = 1
1:0/0/terrains_peering_bit/bottom_right_corner = 1
1:0/0/terrains_peering_bit/bottom_left_corner = 1
1:1/0 = 0
1:1/0/terrain_set = 0
1:1/0/terrain = 1
1:1/0/terrains_peering_bit/bottom_right_corner = 1
1:1/0/terrains_peering_bit/bottom_left_corner = 1
1:1/0/terrains_peering_bit/top_left_corner = 1
1:1/0/terrains_peering_bit/top_right_corner = 1
2:1/animation_separation = Vector2i(2, 0)
2:1/animation_speed = 4.0
2:1/animation_frame_0/duration = 1.0
2:1/animation_frame_1/duration = 1.0
2:1/animation_frame_2/duration = 1.0
2:1/animation_frame_3/duration = 1.0
2:1/0 = 0
2:1/0/terrain_set = 0
2:1/0/terrain = 1
2:1/0/terrains_peering_bit/bottom_left_corner = 1
2:1/0/terrains_peering_bit/top_left_corner = 1
2:2/animation_separation = Vector2i(2, 0)
2:2/animation_speed = 4.0
2:2/animation_frame_0/duration = 1.0
2:2/animation_frame_1/duration = 1.0
2:2/animation_frame_2/duration = 1.0
2:2/animation_frame_3/duration = 1.0
2:2/0 = 0
2:2/0/terrain_set = 0
2:2/0/terrain = 1
2:2/0/terrains_peering_bit/top_left_corner = 1
2:0/animation_separation = Vector2i(2, 0)
2:0/animation_speed = 4.0
2:0/animation_frame_0/duration = 1.0
2:0/animation_frame_1/duration = 1.0
2:0/animation_frame_2/duration = 1.0
2:0/animation_frame_3/duration = 1.0
2:0/0 = 0
2:0/0/terrain_set = 0
2:0/0/terrain = 1
2:0/0/terrains_peering_bit/bottom_left_corner = 1
1:2/animation_separation = Vector2i(2, 0)
1:2/animation_speed = 4.0
1:2/animation_frame_0/duration = 1.0
1:2/animation_frame_1/duration = 1.0
1:2/animation_frame_2/duration = 1.0
1:2/animation_frame_3/duration = 1.0
1:2/0 = 0
1:2/0/terrain_set = 0
1:2/0/terrain = 1
1:2/0/terrains_peering_bit/top_left_corner = 1
1:2/0/terrains_peering_bit/top_right_corner = 1
0:2/animation_separation = Vector2i(2, 0)
0:2/animation_speed = 4.0
0:2/animation_frame_0/duration = 1.0
0:2/animation_frame_1/duration = 1.0
0:2/animation_frame_2/duration = 1.0
0:2/animation_frame_3/duration = 1.0
0:2/0 = 0
0:2/0/terrain_set = 0
0:2/0/terrain = 1
0:2/0/terrains_peering_bit/top_right_corner = 1
0:1/animation_separation = Vector2i(2, 0)
0:1/animation_speed = 4.0
0:1/animation_frame_0/duration = 1.0
0:1/animation_frame_1/duration = 1.0
0:1/animation_frame_2/duration = 1.0
0:1/animation_frame_3/duration = 1.0
0:1/0 = 0
0:1/0/terrain_set = 0
0:1/0/terrain = 1
0:1/0/terrains_peering_bit/bottom_right_corner = 1
0:1/0/terrains_peering_bit/top_right_corner = 1
0:3/animation_separation = Vector2i(1, 0)
0:3/animation_speed = 4.0
0:3/animation_frame_0/duration = 1.0
0:3/animation_frame_1/duration = 1.0
0:3/animation_frame_2/duration = 1.0
0:3/animation_frame_3/duration = 1.0
0:3/0 = 0
1:3/animation_separation = Vector2i(1, 0)
1:3/animation_speed = 4.0
1:3/animation_frame_0/duration = 1.0
1:3/animation_frame_1/duration = 1.0
1:3/animation_frame_2/duration = 1.0
1:3/animation_frame_3/duration = 1.0
1:3/0 = 0
1:4/animation_separation = Vector2i(1, 0)
1:4/animation_speed = 4.0
1:4/animation_frame_0/duration = 1.0
1:4/animation_frame_1/duration = 1.0
1:4/animation_frame_2/duration = 1.0
1:4/animation_frame_3/duration = 1.0
1:4/0 = 0
0:4/animation_separation = Vector2i(1, 0)
0:4/animation_speed = 4.0
0:4/animation_frame_0/duration = 1.0
0:4/animation_frame_1/duration = 1.0
0:4/animation_frame_2/duration = 1.0
0:4/animation_frame_3/duration = 1.0
0:4/0 = 0

[sub_resource type="TileSet" id="TileSet_tlwt5"]
terrain_set_0/mode = 1
terrain_set_0/terrain_0/name = "Region1Style"
terrain_set_0/terrain_0/color = Color(0.5, 0.34375, 0.25, 1)
terrain_set_0/terrain_1/name = "Region2Style"
terrain_set_0/terrain_1/color = Color(0.5, 0.4375, 0.25, 1)
custom_data_layer_0/name = "region"
custom_data_layer_0/type = 2
sources/1 = SubResource("TileSetAtlasSource_0nbtd")
sources/2 = SubResource("TileSetAtlasSource_37qwj")
sources/3 = SubResource("TileSetAtlasSource_ggc52")

[sub_resource type="NavigationPolygon" id="NavigationPolygon_qg546"]
vertices = PackedVector2Array(10.0703, 10.7891, -10.0703, 10.7891, -10.25, -9.89063, 9.89063, -10.25)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-10.2519, -9.89223, 9.89223, -10.2519, 10.0721, 10.7915, -10.0721, 10.7915)])
agent_radius = 0.0

[sub_resource type="NavigationPolygon" id="NavigationPolygon_pf35h"]
vertices = PackedVector2Array(8, 8, -8, 8, -8, -8, 8, -8)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)])
agent_radius = 0.0

[sub_resource type="NavigationPolygon" id="NavigationPolygon_iexy4"]
vertices = PackedVector2Array(10.0703, 10.7891, -10.0703, 10.7891, -10.25, -9.89063, 9.89063, -10.25)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-10.2519, -9.89223, 9.89223, -10.2519, 10.0721, 10.7915, -10.0721, 10.7915)])
agent_radius = 0.0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_k0juu"]
texture = ExtResource("2_4wyf3")
0:0/0 = 0
0:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-11.0383, -7.99799, 10.4318, -8.09364, 10.4588, 7.96588, -11.0319, 7.96589)
0:0/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_qg546")
0:0/0/custom_data_0 = 1.0
0:1/0 = 0
0:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8.00371, -11.511, 8.00372, -11.511, 7.82386, 11.6908, -7.82385, 11.6908)
0:1/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_qg546")
0:1/0/custom_data_0 = 0.7
0:2/0 = 0
0:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-9.98216, -9.53251, 9.62244, -9.53251, 9.44258, 9.53251, -9.98216, 9.1728)
0:2/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_qg546")
0:3/0 = 0
0:3/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_qg546")
0:4/0 = 0
0:4/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_iexy4")
0:5/0 = 0
0:5/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_iexy4")
0:6/0 = 0
0:6/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_pf35h")
0:7/0 = 0
0:7/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_pf35h")
0:8/0 = 0
0:8/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_pf35h")
0:9/0 = 0
0:9/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_pf35h")
0:10/0 = 0
0:10/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_pf35h")
0:11/0 = 0
0:11/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_pf35h")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_lakw3"]
texture = ExtResource("3_7t5mc")
0:0/0 = 0
0:0/0/custom_data_0 = 0.8

[sub_resource type="TileSet" id="TileSet_71j4m"]
physics_layer_0/collision_layer = 1
navigation_layer_0/layers = 1
custom_data_layer_0/name = "speedModifier"
custom_data_layer_0/type = 3
sources/0 = SubResource("TileSetAtlasSource_k0juu")
sources/1 = SubResource("TileSetAtlasSource_lakw3")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_xgqkh"]
texture = ExtResource("5_ctatt")
0:0/0 = 0
0:0/0/custom_data_0 = true
0:1/0 = 0

[sub_resource type="TileSet" id="TileSet_munwf"]
custom_data_layer_0/name = "canBuilding"
custom_data_layer_0/type = 1
sources/0 = SubResource("TileSetAtlasSource_xgqkh")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_4mrxx"]
texture = ExtResource("5_qfnet")
0:0/0 = 0
0:0/0/custom_data_0 = true
0:1/0 = 0

[sub_resource type="TileSet" id="TileSet_qfnet"]
custom_data_layer_0/name = "canDestroyableObject"
custom_data_layer_0/type = 1
sources/0 = SubResource("TileSetAtlasSource_4mrxx")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_rga7q"]
texture = ExtResource("9_8u370")
0:0/0 = 0
0:0/0/custom_data_0 = true
0:1/0 = 0

[sub_resource type="TileSet" id="TileSet_e75v4"]
custom_data_layer_0/name = "canenemy"
custom_data_layer_0/type = 1
sources/0 = SubResource("TileSetAtlasSource_rga7q")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_pm21f"]
texture = ExtResource("6_lakw3")
0:0/0 = 0
0:0/0/custom_data_0 = true
0:1/0 = 0

[sub_resource type="TileSet" id="TileSet_2lf6f"]
custom_data_layer_0/name = "canBridge"
custom_data_layer_0/type = 1
sources/0 = SubResource("TileSetAtlasSource_pm21f")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_7r4gi"]
texture = ExtResource("5_qfnet")
0:0/0 = 0

[sub_resource type="TileSet" id="TileSet_i7141"]
custom_data_layer_0/name = "objectTypePlaced"
custom_data_layer_0/type = 2
sources/0 = SubResource("TileSetAtlasSource_7r4gi")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_i7141"]
texture = ExtResource("6_i7141")
0:0/0 = 0
0:0/0/custom_data_0 = 1
0:1/0 = 0
0:1/0/custom_data_0 = 2
0:2/0 = 0
0:2/0/custom_data_0 = 3
0:3/0 = 0
0:3/0/custom_data_0 = 4
0:4/0 = 0
0:4/0/custom_data_0 = 5
0:5/0 = 0
0:5/0/custom_data_0 = 6

[sub_resource type="TileSet" id="TileSet_4mrxx"]
custom_data_layer_0/name = "region"
custom_data_layer_0/type = 2
sources/0 = SubResource("TileSetAtlasSource_i7141")

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2lf6f"]

[sub_resource type="NavigationPolygon" id="NavigationPolygon_5hglh"]
vertices = PackedVector2Array(402, -168.859, 402, -182.141, 2894.04, -1354.06, 3284.85, 2156.28, 394.141, -257, 402, -264.859, 394.141, -190, 402, -278.141, 394.141, -353, 402, -360.859, 394.141, -286, 402, -374.141, 365, -559.859, 365, -678.211, 394.141, -382, 350.141, -545, 378.969, -382, -1231.26, 2273.73, 394.141, -161, 378.969, -161, 369, -375.352, 369, -359.648, 306, -360.859, 306, -374.141, 329, -545, 202.141, -353, 210, -360.859, 273, -375.352, 273, -359.648, 210, -278.141, 202.141, -286, 282.969, -382, 210, -374.141, 231.859, -545, 253, -545, 306, -168.859, 369, -183.352, 369, -167.648, 298.141, -161, 306, -182.141, 210, -168.859, 273, -183.352, 273, -167.648, 202.141, -161, 210, -182.141, 298.141, -353, 369, -279.352, 369, -263.648, 210, -264.859, 282.969, -353, 253, -565, 261, -565, 298.141, -382, 261, -601, 239, -601, 239, -667.453, 252, -640.648, 240.773, -669, 265, -669, 265, -664, 260.859, -664, 252, -655.141, 321.141, -664, 317, -664, 317, -669, 341.57, -669, 330, -655.141, 343.953, -667.141, 322, -565, 329, -565, 378.969, -353, 378.969, -286, 378.969, -257, 378.969, -190, 282.969, -161, 343.297, -601, 322, -601, 330, -640.648, 186.969, -161, 177, -167.648, -1326.73, -1383.92, 177, -183.352, 272.031, -634, 276.719, -637.133, 279.547, -634, 302.141, -634, 305.273, -637.133, 309.969, -634, 186.969, -190, 177, -263.648, 186.969, -353, 186.969, -286, 177, -359.648, 177, -279.352, 202.141, -257, 298.141, -190, 348.148, -690, 233.203, -690, 218.039, -676.531, 216.961, -559.898, 186.969, -382, 177, -375.352, 320.031, -634, 261.969, -634, 202.141, -382, 186.969, -257, 202.141, -190, 282.969, -190)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3), PackedInt32Array(4, 5, 2, 1, 6), PackedInt32Array(2, 5, 7), PackedInt32Array(8, 9, 2, 7, 10), PackedInt32Array(2, 9, 11), PackedInt32Array(12, 13, 2, 11, 14), PackedInt32Array(15, 12, 14, 16), PackedInt32Array(0, 3, 17, 18), PackedInt32Array(19, 18, 17), PackedInt32Array(20, 21, 22, 23, 24), PackedInt32Array(25, 26, 27, 28, 29, 30), PackedInt32Array(31, 27, 26, 32, 33, 34), PackedInt32Array(35, 36, 37, 17, 38), PackedInt32Array(36, 35, 39), PackedInt32Array(40, 41, 42, 17, 43), PackedInt32Array(41, 40, 44), PackedInt32Array(45, 46, 47, 48, 29, 49), PackedInt32Array(31, 34, 50, 51, 24, 52), PackedInt32Array(53, 54, 55, 56), PackedInt32Array(57, 58, 59, 60), PackedInt32Array(57, 60, 61, 55), PackedInt32Array(62, 63, 64, 65), PackedInt32Array(66, 62, 65, 67), PackedInt32Array(68, 69, 24), PackedInt32Array(70, 8, 10, 71), PackedInt32Array(72, 4, 6, 73), PackedInt32Array(37, 19, 17), PackedInt32Array(74, 38, 17), PackedInt32Array(67, 75, 76, 77), PackedInt32Array(42, 74, 17), PackedInt32Array(78, 43, 17), PackedInt32Array(79, 78, 17), PackedInt32Array(79, 17, 80, 81), PackedInt32Array(82, 83, 84, 68), PackedInt32Array(24, 15, 16, 20), PackedInt32Array(85, 86, 87, 76), PackedInt32Array(24, 23, 52), PackedInt32Array(88, 81, 80, 89), PackedInt32Array(22, 21, 70, 71, 46, 45), PackedInt32Array(28, 49, 29), PackedInt32Array(90, 25, 30, 91), PackedInt32Array(92, 90, 91, 93), PackedInt32Array(94, 48, 47, 72, 73, 36, 95), PackedInt32Array(2, 13, 96), PackedInt32Array(80, 2, 96, 97), PackedInt32Array(80, 97, 98), PackedInt32Array(80, 98, 99, 100, 101), PackedInt32Array(77, 66, 67), PackedInt32Array(102, 77, 76), PackedInt32Array(87, 102, 76), PackedInt32Array(84, 85, 76, 68), PackedInt32Array(51, 53, 103, 82, 68, 24), PackedInt32Array(55, 61, 56), PackedInt32Array(53, 56, 103), PackedInt32Array(33, 32, 104), PackedInt32Array(33, 104, 100, 99), PackedInt32Array(80, 101, 92), PackedInt32Array(80, 92, 93), PackedInt32Array(80, 93, 89), PackedInt32Array(88, 89, 105), PackedInt32Array(88, 105, 94, 106), PackedInt32Array(36, 39, 95), PackedInt32Array(94, 95, 107), PackedInt32Array(106, 94, 107, 41, 44)])
outlines = Array[PackedVector2Array]([PackedVector2Array(2903, -1364, -1337, -1394, -1241, 2284, 3296, 2166)])
source_geometry_mode = 1

[node name="world" type="Node2D"]
y_sort_enabled = true

[node name="Layer1Ground" type="TileMapLayer" parent="."]
z_index = -1
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_tlwt5")

[node name="Layer2Floor_Bridge_SpeedModifier" type="TileMapLayer" parent="." groups=["navigation_polygon_source_geometry_group"]]
z_index = -1
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_71j4m")

[node name="CustomDataLayerManager" parent="." node_paths=PackedStringArray("SpeedModifierLayer", "CanBuildingLayer", "CanDestroyableObjectLayer", "CanPlantLayer", "CanEnemyLayer", "CanBridgeLayer", "CanFishingLayer", "ObjectTypePlacedLayer", "RegionLayer") instance=ExtResource("3_k0juu")]
SpeedModifierLayer = NodePath("../Layer2Floor_Bridge_SpeedModifier")
CanBuildingLayer = NodePath("LayerCanBuilding")
CanDestroyableObjectLayer = NodePath("LayerCanDestroyableObject")
CanPlantLayer = NodePath("LayerCanPlant")
CanEnemyLayer = NodePath("LayerCanEnemy")
CanBridgeLayer = NodePath("LayerCanBridge")
CanFishingLayer = NodePath("LayerCanFishing")
ObjectTypePlacedLayer = NodePath("LayerObjectTypePlaced")
RegionLayer = NodePath("LayerRegion")

[node name="LayerCanBuilding" type="TileMapLayer" parent="CustomDataLayerManager"]
tile_map_data = PackedByteArray("AAACAAoAAAAAAAAAAAACAAsAAAAAAAAAAAACAAwAAAAAAAAAAAACAA0AAAAAAAAAAAADAAoAAAAAAAAAAAADAAsAAAAAAAAAAAADAAwAAAAAAAAAAAADAA0AAAAAAAAAAAAEAAoAAAAAAAAAAAAEAAsAAAAAAAAAAAAEAAwAAAAAAAAAAAAEAA0AAAAAAAAAAAAFAAoAAAAAAAAAAAAFAAsAAAAAAAAAAAAFAAwAAAAAAAAAAAAFAA0AAAAAAAAAAAD//wAAAAAAAAAAAAD//wEAAAAAAAAAAAD//wIAAAAAAAAAAAD//wMAAAAAAAAAAAD//wQAAAAAAAAAAAD//wUAAAAAAAAAAAD//wYAAAAAAAAAAAD//wcAAAAAAAAAAAD//wgAAAAAAAAAAAD//wkAAAAAAAAAAAD//woAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAYAAAAAAAAAAAAAAAcAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAAkAAAAAAAAAAAAAAAoAAAAAAAAAAAABAAAAAAAAAAAAAAABAAEAAAAAAAAAAAABAAIAAAAAAAAAAAABAAMAAAAAAAAAAAABAAQAAAAAAAAAAAABAAUAAAAAAAAAAAABAAYAAAAAAAAAAAABAAcAAAAAAAAAAAABAAgAAAAAAAAAAAABAAkAAAAAAAAAAAABAAoAAAAAAAAAAAABAP//AAAAAAAAAAACAP7/AAAAAAAAAAACAP//AAAAAAAAAAACAAAAAAAAAAAAAAACAAEAAAAAAAAAAAACAAIAAAAAAAAAAAACAAMAAAAAAAAAAAACAAQAAAAAAAAAAAACAAUAAAAAAAAAAAACAAYAAAAAAAAAAAACAAcAAAAAAAAAAAACAAgAAAAAAAAAAAACAAkAAAAAAAAAAAADAP7/AAAAAAAAAAADAP//AAAAAAAAAAADAAAAAAAAAAAAAAADAAEAAAAAAAAAAAADAAIAAAAAAAAAAAADAAMAAAAAAAAAAAADAAQAAAAAAAAAAAADAAUAAAAAAAAAAAADAAYAAAAAAAAAAAADAAcAAAAAAAAAAAADAAgAAAAAAAAAAAADAAkAAAAAAAAAAAAEAP7/AAAAAAAAAAAEAP//AAAAAAAAAAAEAAAAAAAAAAAAAAAEAAEAAAAAAAAAAAAEAAIAAAAAAAAAAAAEAAMAAAAAAAAAAAAEAAQAAAAAAAAAAAAEAAUAAAAAAAAAAAAEAAYAAAAAAAAAAAAEAAcAAAAAAAAAAAAEAAgAAAAAAAAAAAAEAAkAAAAAAAAAAAAFAP7/AAAAAAAAAAAFAP//AAAAAAAAAAAFAAAAAAAAAAAAAAAFAAEAAAAAAAAAAAAFAAIAAAAAAAAAAAAFAAMAAAAAAAAAAAAFAAQAAAAAAAAAAAAFAAUAAAAAAAAAAAAFAAYAAAAAAAAAAAAFAAcAAAAAAAAAAAAFAAgAAAAAAAAAAAAFAAkAAAAAAAAAAAAGAP7/AAAAAAAAAAAGAP//AAAAAAAAAAAGAAAAAAAAAAAAAAAGAAEAAAAAAAAAAAAGAAIAAAAAAAAAAAAGAAMAAAAAAAAAAAAGAAQAAAAAAAAAAAAGAAUAAAAAAAAAAAAGAAYAAAAAAAAAAAAGAAcAAAAAAAAAAAAGAAgAAAAAAAAAAAAGAAkAAAAAAAAAAAAGAAoAAAAAAAAAAAAGAAsAAAAAAAAAAAAHAP7/AAAAAAAAAAAHAP//AAAAAAAAAAAHAAAAAAAAAAAAAAAHAAEAAAAAAAAAAAAHAAIAAAAAAAAAAAAHAAMAAAAAAAAAAAAHAAQAAAAAAAAAAAAHAAUAAAAAAAAAAAAHAAYAAAAAAAAAAAAHAAcAAAAAAAAAAAAHAAgAAAAAAAAAAAAHAAkAAAAAAAAAAAAHAAoAAAAAAAAAAAAHAAsAAAAAAAAAAAAIAP7/AAAAAAAAAAAIAP//AAAAAAAAAAAIAAAAAAAAAAAAAAAIAAEAAAAAAAAAAAAIAAIAAAAAAAAAAAAIAAMAAAAAAAAAAAAIAAQAAAAAAAAAAAAIAAUAAAAAAAAAAAAIAAYAAAAAAAAAAAAIAAcAAAAAAAAAAAAIAAgAAAAAAAAAAAAIAAkAAAAAAAAAAAAIAAoAAAAAAAAAAAAIAAsAAAAAAAAAAAAJAP7/AAAAAAAAAAAJAP//AAAAAAAAAAAJAAAAAAAAAAAAAAAJAAEAAAAAAAAAAAAJAAIAAAAAAAAAAAAJAAMAAAAAAAAAAAAJAAQAAAAAAAAAAAAJAAUAAAAAAAAAAAAJAAYAAAAAAAAAAAAJAAcAAAAAAAAAAAAJAAgAAAAAAAAAAAAJAAkAAAAAAAAAAAAJAAoAAAAAAAAAAAAJAAsAAAAAAAAAAAAKAP7/AAAAAAAAAAAKAP//AAAAAAAAAAAKAAAAAAAAAAAAAAAKAAEAAAAAAAAAAAAKAAIAAAAAAAAAAAAKAAMAAAAAAAAAAAAKAAQAAAAAAAAAAAAKAAUAAAAAAAAAAAAKAAYAAAAAAAAAAAAKAAcAAAAAAAAAAAAKAAgAAAAAAAAAAAAKAAkAAAAAAAAAAAAKAAoAAAAAAAAAAAAKAAsAAAAAAAAAAAALAAYAAAAAAAAAAAALAAcAAAAAAAAAAAALAAgAAAAAAAAAAAALAAkAAAAAAAAAAAALAAoAAAAAAAAAAAALAAsAAAAAAAAAAAALAAwAAAAAAAAAAAALAA0AAAAAAAAAAAAMAAYAAAAAAAAAAAAMAAcAAAAAAAAAAAAMAAgAAAAAAAAAAAAMAAkAAAAAAAAAAAAMAAoAAAAAAAAAAAAMAAsAAAAAAAAAAAAMAAwAAAAAAAAAAAAMAA0AAAAAAAAAAAANAAYAAAAAAAAAAAANAAcAAAAAAAAAAAANAAgAAAAAAAAAAAANAAkAAAAAAAAAAAANAAoAAAAAAAAAAAANAAsAAAAAAAAAAAANAAwAAAAAAAAAAAANAA0AAAAAAAAAAAAOAA0AAAAAAAAAAAAKAA0AAAAAAAAAAAAKAAwAAAAAAAAAAAALAP7/AAAAAAAAAAALAP//AAAAAAAAAAALAAAAAAAAAAAAAAALAAEAAAAAAAAAAAALAAIAAAAAAAAAAAAMAP7/AAAAAAAAAAAMAP//AAAAAAAAAAAMAAAAAAAAAAAAAAAMAAEAAAAAAAAAAAAMAAIAAAAAAAAAAAANAP7/AAAAAAAAAAANAP//AAAAAAAAAAANAAAAAAAAAAAAAAANAAEAAAAAAAAAAAANAAIAAAAAAAAAAAAOAP7/AAAAAAAAAAAOAP//AAAAAAAAAAAOAAAAAAAAAAAAAAAOAAEAAAAAAAAAAAAOAAIAAAAAAAAAAAAOAAMAAAAAAAAAAAAOAAQAAAAAAAAAAAAOAAUAAAAAAAAAAAASAAAAAAAAAAAAAAASAAEAAAAAAAAAAAASAAIAAAAAAAAAAAASAAMAAAAAAAAAAAASAAQAAAAAAAAAAAASAAUAAAAAAAAAAAASAAYAAAAAAAAAAAASAAcAAAAAAAAAAAASAAgAAAAAAAAAAAASAAkAAAAAAAAAAAASAAoAAAAAAAAAAAASAAsAAAAAAAAAAAASAAwAAAAAAAAAAAASAA0AAAAAAAAAAAATAAAAAAAAAAAAAAATAAEAAAAAAAAAAAATAAIAAAAAAAAAAAATAAMAAAAAAAAAAAATAAQAAAAAAAAAAAATAAUAAAAAAAAAAAATAAYAAAAAAAAAAAATAAcAAAAAAAAAAAATAAgAAAAAAAAAAAATAAkAAAAAAAAAAAATAAoAAAAAAAAAAAATAAsAAAAAAAAAAAATAAwAAAAAAAAAAAATAA0AAAAAAAAAAAAUAAAAAAAAAAAAAAAUAAEAAAAAAAAAAAAUAAIAAAAAAAAAAAAUAAMAAAAAAAAAAAAUAAQAAAAAAAAAAAAUAAUAAAAAAAAAAAAUAAYAAAAAAAAAAAAUAAcAAAAAAAAAAAAUAAgAAAAAAAAAAAAUAAkAAAAAAAAAAAAUAAoAAAAAAAAAAAAUAAsAAAAAAAAAAAAUAAwAAAAAAAAAAAAUAA0AAAAAAAAAAAAVAAAAAAAAAAAAAAAVAAEAAAAAAAAAAAAVAAIAAAAAAAAAAAAVAAMAAAAAAAAAAAAVAAQAAAAAAAAAAAAVAAUAAAAAAAAAAAAVAAYAAAAAAAAAAAAVAAcAAAAAAAAAAAAVAAgAAAAAAAAAAAAVAAkAAAAAAAAAAAAVAAoAAAAAAAAAAAAVAAsAAAAAAAAAAAAVAAwAAAAAAAAAAAAVAA0AAAAAAAAAAAAWAAAAAAAAAAAAAAAWAAEAAAAAAAAAAAAWAAIAAAAAAAAAAAAWAAMAAAAAAAAAAAAWAAQAAAAAAAAAAAAWAAUAAAAAAAAAAAAWAAYAAAAAAAAAAAAWAAcAAAAAAAAAAAAWAAgAAAAAAAAAAAAWAAkAAAAAAAAAAAAWAAoAAAAAAAAAAAAWAAsAAAAAAAAAAAAWAAwAAAAAAAAAAAAWAA0AAAAAAAAAAAAXAAAAAAAAAAAAAAAXAAEAAAAAAAAAAAAXAAIAAAAAAAAAAAAXAAMAAAAAAAAAAAAXAAQAAAAAAAAAAAAXAAUAAAAAAAAAAAAXAAYAAAAAAAAAAAAXAAcAAAAAAAAAAAAXAAgAAAAAAAAAAAAXAAkAAAAAAAAAAAAXAAoAAAAAAAAAAAAXAAsAAAAAAAAAAAAXAAwAAAAAAAAAAAAXAA0AAAAAAAAAAAATAP7/AAAAAAAAAAATAP//AAAAAAAAAAAUAP7/AAAAAAAAAAAUAP//AAAAAAAAAAAVAP7/AAAAAAAAAAAVAP//AAAAAAAAAAAWAP7/AAAAAAAAAAAWAP//AAAAAAAAAAAXAP7/AAAAAAAAAAAXAP//AAAAAAAAAAAYAP7/AAAAAAAAAAAYAP//AAAAAAAAAAAYAAAAAAAAAAAAAAAYAAEAAAAAAAAAAAAYAAIAAAAAAAAAAAAYAAMAAAAAAAAAAAAYAAQAAAAAAAAAAAAYAAUAAAAAAAAAAAAYAAYAAAAAAAAAAAAYAAcAAAAAAAAAAAAYAAgAAAAAAAAAAAAYAAkAAAAAAAAAAAAYAAoAAAAAAAAAAAAYAAsAAAAAAAAAAAAZAP7/AAAAAAAAAAAZAP//AAAAAAAAAAAZAAAAAAAAAAAAAAAZAAEAAAAAAAAAAAAZAAIAAAAAAAAAAAAZAAMAAAAAAAAAAAAZAAQAAAAAAAAAAAAZAAUAAAAAAAAAAAAZAAYAAAAAAAAAAAAZAAcAAAAAAAAAAAAZAAgAAAAAAAAAAAAZAAkAAAAAAAAAAAAZAAoAAAAAAAAAAAAZAAsAAAAAAAAAAAAaAAUAAAAAAAAAAAAaAAYAAAAAAAAAAAAaAAcAAAAAAAAAAAAaAAgAAAAAAAAAAAAaAAkAAAAAAAAAAAAaAAoAAAAAAAAAAAAaAAsAAAAAAAAAAAAbAAUAAAAAAAAAAAAbAAYAAAAAAAAAAAAbAAcAAAAAAAAAAAAbAAgAAAAAAAAAAAAbAAkAAAAAAAAAAAAbAAoAAAAAAAAAAAAbAAsAAAAAAAAAAAAcAAUAAAAAAAAAAAAcAAYAAAAAAAAAAAAcAAcAAAAAAAAAAAAcAAgAAAAAAAAAAAAcAAkAAAAAAAAAAAAcAAoAAAAAAAAAAAAcAAsAAAAAAAAAAAAdAAUAAAAAAAAAAAAdAAYAAAAAAAAAAAAdAAcAAAAAAAAAAAAdAAgAAAAAAAAAAAAdAAkAAAAAAAAAAAAdAAoAAAAAAAAAAAAdAAsAAAAAAAAAAAAeAAUAAAAAAAAAAAAeAAYAAAAAAAAAAAAeAAcAAAAAAAAAAAAeAAgAAAAAAAAAAAAeAAkAAAAAAAAAAAAeAAoAAAAAAAAAAAAeAAsAAAAAAAAAAAAfAAUAAAAAAAAAAAAfAAYAAAAAAAAAAAAfAAcAAAAAAAAAAAAfAAgAAAAAAAAAAAAfAAkAAAAAAAAAAAAfAAoAAAAAAAAAAAAfAAsAAAAAAAAAAAAgAAUAAAAAAAAAAAAgAAYAAAAAAAAAAAAgAAcAAAAAAAAAAAAgAAgAAAAAAAAAAAAgAAkAAAAAAAAAAAAgAAoAAAAAAAAAAAAgAAsAAAAAAAAAAAAhAAUAAAAAAAAAAAAhAAYAAAAAAAAAAAAhAAcAAAAAAAAAAAAhAAgAAAAAAAAAAAAhAAkAAAAAAAAAAAAhAAoAAAAAAAAAAAAhAAsAAAAAAAAAAAAdAAwAAAAAAAAAAAAdAA0AAAAAAAAAAAAeAAwAAAAAAAAAAAAeAA0AAAAAAAAAAAAfAAwAAAAAAAAAAAAfAA0AAAAAAAAAAAAgAAwAAAAAAAAAAAAgAA0AAAAAAAAAAAAhAAwAAAAAAAAAAAAhAA0AAAAAAAAAAAAgAP7/AAAAAAAAAAAgAP//AAAAAAAAAAAgAAAAAAAAAAAAAAAgAAEAAAAAAAAAAAAgAAIAAAAAAAAAAAAgAAMAAAAAAAAAAAAgAAQAAAAAAAAAAAAhAP7/AAAAAAAAAAAhAP//AAAAAAAAAAAhAAAAAAAAAAAAAAAhAAEAAAAAAAAAAAAhAAIAAAAAAAAAAAAhAAMAAAAAAAAAAAAhAAQAAAAAAAAAAAAaAP7/AAAAAAAAAAAbAP7/AAAAAAAAAAAcAP7/AAAAAAAAAAAdAP7/AAAAAAAAAAAeAP7/AAAAAAAAAAAfAP7/AAAAAAAAAAASABEAAAAAAAAAAAASABIAAAAAAAAAAAASABMAAAAAAAAAAAASABQAAAAAAAAAAAASABUAAAAAAAAAAAASABYAAAAAAAAAAAASABcAAAAAAAAAAAASABgAAAAAAAAAAAASABkAAAAAAAAAAAASABoAAAAAAAAAAAASABsAAAAAAAAAAAASABwAAAAAAAAAAAASAB0AAAAAAAAAAAASAB4AAAAAAAAAAAASAB8AAAAAAAAAAAATABEAAAAAAAAAAAATABIAAAAAAAAAAAATABMAAAAAAAAAAAATABQAAAAAAAAAAAATABUAAAAAAAAAAAATABYAAAAAAAAAAAATABcAAAAAAAAAAAATABgAAAAAAAAAAAATABkAAAAAAAAAAAATABoAAAAAAAAAAAATABsAAAAAAAAAAAATABwAAAAAAAAAAAATAB0AAAAAAAAAAAATAB4AAAAAAAAAAAATAB8AAAAAAAAAAAAUABEAAAAAAAAAAAAUABIAAAAAAAAAAAAUABMAAAAAAAAAAAAUABQAAAAAAAAAAAAUABUAAAAAAAAAAAAUABYAAAAAAAAAAAAUABcAAAAAAAAAAAAUABgAAAAAAAAAAAAUABkAAAAAAAAAAAAUABoAAAAAAAAAAAAUABsAAAAAAAAAAAAUABwAAAAAAAAAAAAUAB0AAAAAAAAAAAAUAB4AAAAAAAAAAAAUAB8AAAAAAAAAAAAVABEAAAAAAAAAAAAVABIAAAAAAAAAAAAVABMAAAAAAAAAAAAVABQAAAAAAAAAAAAVABUAAAAAAAAAAAAVABYAAAAAAAAAAAAVABcAAAAAAAAAAAAVABgAAAAAAAAAAAAVABkAAAAAAAAAAAAVABoAAAAAAAAAAAAVABsAAAAAAAAAAAAVABwAAAAAAAAAAAAVAB0AAAAAAAAAAAAVAB4AAAAAAAAAAAAVAB8AAAAAAAAAAAAWABEAAAAAAAAAAAAWABIAAAAAAAAAAAAWABMAAAAAAAAAAAAWABQAAAAAAAAAAAAWABUAAAAAAAAAAAAWABYAAAAAAAAAAAAWABcAAAAAAAAAAAAWABgAAAAAAAAAAAAWABkAAAAAAAAAAAAWABoAAAAAAAAAAAAWABsAAAAAAAAAAAAWABwAAAAAAAAAAAAWAB0AAAAAAAAAAAAWAB4AAAAAAAAAAAAWAB8AAAAAAAAAAAAXABEAAAAAAAAAAAAXABIAAAAAAAAAAAAXABMAAAAAAAAAAAAXABQAAAAAAAAAAAAXABUAAAAAAAAAAAAXABYAAAAAAAAAAAAXABcAAAAAAAAAAAAXABgAAAAAAAAAAAAXABkAAAAAAAAAAAAXABoAAAAAAAAAAAAXABsAAAAAAAAAAAAXABwAAAAAAAAAAAAXAB0AAAAAAAAAAAAXAB4AAAAAAAAAAAAXAB8AAAAAAAAAAAAYABEAAAAAAAAAAAAYABIAAAAAAAAAAAAYABMAAAAAAAAAAAAYABQAAAAAAAAAAAAYABUAAAAAAAAAAAAYABYAAAAAAAAAAAAYABcAAAAAAAAAAAAYABgAAAAAAAAAAAAYABkAAAAAAAAAAAAYABoAAAAAAAAAAAAYABsAAAAAAAAAAAAYABwAAAAAAAAAAAAYAB0AAAAAAAAAAAAYAB4AAAAAAAAAAAAYAB8AAAAAAAAAAAAZABEAAAAAAAAAAAAZABIAAAAAAAAAAAAZABMAAAAAAAAAAAAZABQAAAAAAAAAAAAZABUAAAAAAAAAAAAZABYAAAAAAAAAAAAZABcAAAAAAAAAAAAZABgAAAAAAAAAAAAZABkAAAAAAAAAAAAZABoAAAAAAAAAAAAZABsAAAAAAAAAAAAZABwAAAAAAAAAAAAZAB0AAAAAAAAAAAAZAB4AAAAAAAAAAAAZAB8AAAAAAAAAAAAaABEAAAAAAAAAAAAaABIAAAAAAAAAAAAaABMAAAAAAAAAAAAaABQAAAAAAAAAAAAaABUAAAAAAAAAAAAaABYAAAAAAAAAAAAaABcAAAAAAAAAAAAaABgAAAAAAAAAAAAaABkAAAAAAAAAAAAaABoAAAAAAAAAAAAaABsAAAAAAAAAAAAaABwAAAAAAAAAAAAaAB0AAAAAAAAAAAAaAB4AAAAAAAAAAAAaAB8AAAAAAAAAAAAbABEAAAAAAAAAAAAbABIAAAAAAAAAAAAbABMAAAAAAAAAAAAbABQAAAAAAAAAAAAbABUAAAAAAAAAAAAbABYAAAAAAAAAAAAbABcAAAAAAAAAAAAbABgAAAAAAAAAAAAbABkAAAAAAAAAAAAbABoAAAAAAAAAAAAbABsAAAAAAAAAAAAbABwAAAAAAAAAAAAbAB0AAAAAAAAAAAAbAB4AAAAAAAAAAAAbAB8AAAAAAAAAAAAcABEAAAAAAAAAAAAcABIAAAAAAAAAAAAcABMAAAAAAAAAAAAcABQAAAAAAAAAAAAdABEAAAAAAAAAAAAdABIAAAAAAAAAAAAdABMAAAAAAAAAAAAdABQAAAAAAAAAAAAeABEAAAAAAAAAAAAeABIAAAAAAAAAAAAeABMAAAAAAAAAAAAeABQAAAAAAAAAAAAfABEAAAAAAAAAAAAfABIAAAAAAAAAAAAfABMAAAAAAAAAAAAfABQAAAAAAAAAAAAgABEAAAAAAAAAAAAgABIAAAAAAAAAAAAgABMAAAAAAAAAAAAgABQAAAAAAAAAAAAhABEAAAAAAAAAAAAhABIAAAAAAAAAAAAhABMAAAAAAAAAAAAhABQAAAAAAAAAAAAgABkAAAAAAAAAAAAgABoAAAAAAAAAAAAgABsAAAAAAAAAAAAgABwAAAAAAAAAAAAgAB0AAAAAAAAAAAAgAB4AAAAAAAAAAAAgAB8AAAAAAAAAAAAgACAAAAAAAAAAAAAhABkAAAAAAAAAAAAhABoAAAAAAAAAAAAhABsAAAAAAAAAAAAhABwAAAAAAAAAAAAhAB0AAAAAAAAAAAAhAB4AAAAAAAAAAAAhAB8AAAAAAAAAAAAhACAAAAAAAAAAAAAdAB0AAAAAAAAAAAAdAB4AAAAAAAAAAAAdAB8AAAAAAAAAAAAdACAAAAAAAAAAAAAeAB0AAAAAAAAAAAAeAB4AAAAAAAAAAAAeAB8AAAAAAAAAAAAeACAAAAAAAAAAAAAfAB0AAAAAAAAAAAAfAB4AAAAAAAAAAAAfAB8AAAAAAAAAAAAfACAAAAAAAAAAAAAcAB0AAAAAAAAAAAAcAB4AAAAAAAAAAAAcAB8AAAAAAAAAAAASACAAAAAAAAAAAAATACAAAAAAAAAAAAAUACAAAAAAAAAAAAAVACAAAAAAAAAAAAD//xEAAAAAAAAAAAD//xIAAAAAAAAAAAD//xMAAAAAAAAAAAD//xQAAAAAAAAAAAD//xUAAAAAAAAAAAD//xYAAAAAAAAAAAD//xcAAAAAAAAAAAD//xgAAAAAAAAAAAD//xkAAAAAAAAAAAD//xoAAAAAAAAAAAD//xsAAAAAAAAAAAD//xwAAAAAAAAAAAD//x0AAAAAAAAAAAD//x4AAAAAAAAAAAD//x8AAAAAAAAAAAD//yAAAAAAAAAAAAAAABEAAAAAAAAAAAAAABIAAAAAAAAAAAAAABMAAAAAAAAAAAAAABQAAAAAAAAAAAAAABUAAAAAAAAAAAAAABYAAAAAAAAAAAAAABcAAAAAAAAAAAAAABgAAAAAAAAAAAAAABkAAAAAAAAAAAAAABoAAAAAAAAAAAAAABsAAAAAAAAAAAAAABwAAAAAAAAAAAAAAB0AAAAAAAAAAAAAAB4AAAAAAAAAAAAAAB8AAAAAAAAAAAAAACAAAAAAAAAAAAABABEAAAAAAAAAAAABABIAAAAAAAAAAAABABMAAAAAAAAAAAABABQAAAAAAAAAAAABABUAAAAAAAAAAAABABYAAAAAAAAAAAABABcAAAAAAAAAAAABABgAAAAAAAAAAAABABkAAAAAAAAAAAABABoAAAAAAAAAAAABABsAAAAAAAAAAAABABwAAAAAAAAAAAABAB0AAAAAAAAAAAABAB4AAAAAAAAAAAABAB8AAAAAAAAAAAABACAAAAAAAAAAAAACABsAAAAAAAAAAAACABwAAAAAAAAAAAACAB0AAAAAAAAAAAACAB4AAAAAAAAAAAADABsAAAAAAAAAAAADABwAAAAAAAAAAAADAB0AAAAAAAAAAAADAB4AAAAAAAAAAAAEABsAAAAAAAAAAAAEABwAAAAAAAAAAAAEAB0AAAAAAAAAAAAEAB4AAAAAAAAAAAAFABsAAAAAAAAAAAAFABwAAAAAAAAAAAAFAB0AAAAAAAAAAAAFAB4AAAAAAAAAAAAGABsAAAAAAAAAAAAGABwAAAAAAAAAAAAGAB0AAAAAAAAAAAAGAB4AAAAAAAAAAAAHABsAAAAAAAAAAAAHABwAAAAAAAAAAAAHAB0AAAAAAAAAAAAHAB4AAAAAAAAAAAAIABsAAAAAAAAAAAAIABwAAAAAAAAAAAAIAB0AAAAAAAAAAAAIAB4AAAAAAAAAAAAJABsAAAAAAAAAAAAJABwAAAAAAAAAAAAJAB0AAAAAAAAAAAAJAB4AAAAAAAAAAAAKABsAAAAAAAAAAAAKABwAAAAAAAAAAAAKAB0AAAAAAAAAAAAKAB4AAAAAAAAAAAALABsAAAAAAAAAAAALABwAAAAAAAAAAAALAB0AAAAAAAAAAAALAB4AAAAAAAAAAAAMABsAAAAAAAAAAAAMABwAAAAAAAAAAAAMAB0AAAAAAAAAAAANABsAAAAAAAAAAAANABwAAAAAAAAAAAANAB0AAAAAAAAAAAAOABsAAAAAAAAAAAAOABwAAAAAAAAAAAAOAB0AAAAAAAAAAAAKAB8AAAAAAAAAAAAKACAAAAAAAAAAAAALAB8AAAAAAAAAAAALACAAAAAAAAAAAAACABIAAAAAAAAAAAADABIAAAAAAAAAAAAEABIAAAAAAAAAAAAFABIAAAAAAAAAAAAGABIAAAAAAAAAAAAHABIAAAAAAAAAAAAIABIAAAAAAAAAAAAJABIAAAAAAAAAAAAKABIAAAAAAAAAAAALABIAAAAAAAAAAAAMABIAAAAAAAAAAAANABIAAAAAAAAAAAAOABIAAAAAAAAAAAANABMAAAAAAAAAAAANABQAAAAAAAAAAAANABUAAAAAAAAAAAANABYAAAAAAAAAAAANABcAAAAAAAAAAAANABgAAAAAAAAAAAANABkAAAAAAAAAAAANABoAAAAAAAAAAAAOABMAAAAAAAAAAAAOABQAAAAAAAAAAAAOABUAAAAAAAAAAAAOABYAAAAAAAAAAAAOABcAAAAAAAAAAAAOABgAAAAAAAAAAAAOABkAAAAAAAAAAAAOABoAAAAAAAAAAAACABMAAAAAAAAAAAACABQAAAAAAAAAAAACABUAAAAAAAAAAAACABYAAAAAAAAAAAACABcAAAAAAAAAAAACABgAAAAAAAAAAAACABkAAAAAAAAAAAACABoAAAAAAAAAAAACABEAAAAAAAAAAAADABEAAAAAAAAAAAAEABEAAAAAAAAAAAAMABEAAAAAAAAAAAANABEAAAAAAAAAAAAOABEAAAAAAAAAAAA=")
tile_set = SubResource("TileSet_munwf")

[node name="LayerCanDestroyableObject" type="TileMapLayer" parent="CustomDataLayerManager"]
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_qfnet")

[node name="LayerCanEnemy" type="TileMapLayer" parent="CustomDataLayerManager"]
tile_map_data = PackedByteArray("AAAUAO7/AAAAAAAAAAAVAO//AAAAAAAAAAAWAPD/AAAAAAAAAAAWAPH/AAAAAAAAAAAXAPL/AAAAAAAAAAAXAPP/AAAAAAAAAAAXAPT/AAAAAAAAAAAXAPX/AAAAAAAAAAAYAPX/AAAAAAAAAAAZAPX/AAAAAAAAAAAaAPX/AAAAAAAAAAAbAPX/AAAAAAAAAAAcAPX/AAAAAAAAAAAdAPX/AAAAAAAAAAAeAPX/AAAAAAAAAAAfAPX/AAAAAAAAAAAgAPX/AAAAAAAAAAAfAPT/AAAAAAAAAAAfAPP/AAAAAAAAAAAeAPP/AAAAAAAAAAAfAPL/AAAAAAAAAAAfAPH/AAAAAAAAAAAfAPD/AAAAAAAAAAAeAPD/AAAAAAAAAAAeAO//AAAAAAAAAAAeAO7/AAAAAAAAAAAdAO7/AAAAAAAAAAAdAO3/AAAAAAAAAAAdAOz/AAAAAAAAAAAcAOz/AAAAAAAAAAAbAO3/AAAAAAAAAAAaAO3/AAAAAAAAAAAZAO3/AAAAAAAAAAAYAO3/AAAAAAAAAAAXAO7/AAAAAAAAAAAWAO7/AAAAAAAAAAA=")
tile_set = SubResource("TileSet_e75v4")

[node name="LayerCanPlant" type="TileMapLayer" parent="CustomDataLayerManager"]

[node name="LayerCanBridge" type="TileMapLayer" parent="CustomDataLayerManager"]
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_2lf6f")

[node name="LayerCanFishing" type="TileMapLayer" parent="CustomDataLayerManager"]

[node name="LayerObjectTypePlaced" type="TileMapLayer" parent="CustomDataLayerManager"]
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_i7141")

[node name="LayerRegion" type="TileMapLayer" parent="CustomDataLayerManager"]
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_4mrxx")

[node name="LayerYSort" type="TileMapLayer" parent="."]
y_sort_enabled = true

[node name="Buildings" type="Node2D" parent="."]
y_sort_enabled = true

[node name="Player" parent="." node_paths=PackedStringArray("CustomDataManager") instance=ExtResource("2_rwgxs")]
position = Vector2(119, 83)
CustomDataManager = NodePath("../CustomDataLayerManager")

[node name="PlantingManager" parent="." instance=ExtResource("13_plantingmanager")]
y_sort_enabled = true
PlantScene = ExtResource("19_f17e3")
SeedMoveScene = ExtResource("20_x2olw")

[node name="RegionManagers" type="Node2D" parent="."]
y_sort_enabled = true

[node name="Region1Manager" parent="RegionManagers" instance=ExtResource("7_7r4gi")]
BaseSpawnInterval = 10.0
BerryBushScene = ExtResource("8_dss4m")
GreenBushScene = ExtResource("26_18clu")
BigGreenBushScene = ExtResource("19_1jxku")
Rock2Scene = ExtResource("9_7t5mc")
BrownMushroomScene = ExtResource("27_d46x8")
RabbitSpawnInterval = 5.0
RabbitScene = ExtResource("14_o8fc1")

[node name="Region2Manager" parent="RegionManagers" instance=ExtResource("7_region2")]
BaseSpawnInterval = 10.0
BerryBushScene = ExtResource("8_dss4m")
GreenBushScene = ExtResource("26_18clu")
BigGreenBushScene = ExtResource("19_1jxku")
Rock2Scene = ExtResource("9_7t5mc")
BrownMushroomScene = ExtResource("27_d46x8")
RabbitScene = ExtResource("14_o8fc1")

[node name="Region3Manager" parent="RegionManagers" instance=ExtResource("7_region3")]
BaseSpawnInterval = 10.0
BerryBushScene = ExtResource("8_dss4m")
GreenBushScene = ExtResource("26_18clu")
BigGreenBushScene = ExtResource("19_1jxku")
Rock2Scene = ExtResource("9_7t5mc")
BrownMushroomScene = ExtResource("27_d46x8")
RabbitScene = ExtResource("14_o8fc1")

[node name="Region4Manager" parent="RegionManagers" instance=ExtResource("7_region4")]
BaseSpawnInterval = 10.0
BerryBushScene = ExtResource("8_dss4m")
GreenBushScene = ExtResource("26_18clu")
BigGreenBushScene = ExtResource("19_1jxku")
Rock2Scene = ExtResource("9_7t5mc")
BrownMushroomScene = ExtResource("27_d46x8")
RabbitScene = ExtResource("14_o8fc1")

[node name="Region5Manager" parent="RegionManagers" groups=["RegionManager"] instance=ExtResource("7_region5")]
RockSpawnWeight = 10
BerryBushSpawnWeight = 5
Tree2Scene = ExtResource("24_eo1xk")
CopperRockScene = ExtResource("25_18clu")
GreenBush2Scene = ExtResource("26_4wdv8")
BrownMushroomScene = ExtResource("27_d46x8")
RockScene = ExtResource("25_eo1xk")
Rock2Scene = ExtResource("9_7t5mc")
BerryBushScene = ExtResource("8_dss4m")
EnemySpawnInterval = 2.0

[node name="Region6Manager" parent="RegionManagers" groups=["RegionManager"] instance=ExtResource("28_d46x8")]

[node name="Region7Manager" parent="RegionManagers" groups=["RegionManager"] instance=ExtResource("7_region7")]
RockSpawnWeight = 10
BerryBushSpawnWeight = 5
Tree2Scene = ExtResource("24_eo1xk")
CopperRockScene = ExtResource("25_18clu")
GreenBush2Scene = ExtResource("26_4wdv8")
BrownMushroomScene = ExtResource("27_d46x8")
RockScene = ExtResource("25_eo1xk")
Rock2Scene = ExtResource("9_7t5mc")
BerryBushScene = ExtResource("8_dss4m")

[node name="Region8Manager" parent="RegionManagers" groups=["RegionManager"] instance=ExtResource("7_region8")]
RockSpawnWeight = 10
BerryBushSpawnWeight = 5
Tree2Scene = ExtResource("24_eo1xk")
CopperRockScene = ExtResource("25_18clu")
GreenBush2Scene = ExtResource("26_4wdv8")
BrownMushroomScene = ExtResource("27_d46x8")
RockScene = ExtResource("25_eo1xk")
Rock2Scene = ExtResource("9_7t5mc")
BerryBushScene = ExtResource("8_dss4m")

[node name="DroppedResourceManager" parent="." instance=ExtResource("4_dropped_resource_manager")]

[node name="SelectedToolPanel" parent="." instance=ExtResource("9_7r4gi")]

[node name="InventoryMenu" parent="." instance=ExtResource("10_w7kh3")]

[node name="PlayerStatusPanel" parent="." instance=ExtResource("11_jhx03")]

[node name="BuildingPlacer" parent="." instance=ExtResource("11_buildingplacer")]

[node name="BuildingManager" parent="." instance=ExtResource("12_buildingmanager")]
WorkbenchScene = ExtResource("27_x2olw")

[node name="ChestManager" parent="." instance=ExtResource("13_chestmanager")]

[node name="RegionUnlockManager" type="Node" parent="."]
script = ExtResource("22_regionunlock")

[node name="RegionBlockers" type="Node2D" parent="."]
visible = false
z_index = 1

[node name="Region2" type="Sprite2D" parent="RegionBlockers"]
position = Vector2(419.5, 96)
scale = Vector2(1.15234, 1.125)
texture = ExtResource("19_pm21f")
script = ExtResource("21_oo54l")

[node name="StaticBody2D" type="StaticBody2D" parent="RegionBlockers/Region2"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="RegionBlockers/Region2/StaticBody2D"]
scale = Vector2(12.785, 12.785)
shape = SubResource("RectangleShape2D_2lf6f")

[node name="Region3" type="Sprite2D" parent="RegionBlockers"]
position = Vector2(418.5, 403)
scale = Vector2(1.14453, 1.14844)
texture = ExtResource("19_pm21f")
script = ExtResource("21_oo54l")
RegionId = 3

[node name="StaticBody2D" type="StaticBody2D" parent="RegionBlockers/Region3"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="RegionBlockers/Region3/StaticBody2D"]
scale = Vector2(12.785, 12.785)
shape = SubResource("RectangleShape2D_2lf6f")

[node name="Region4" type="Sprite2D" parent="RegionBlockers"]
position = Vector2(111, 402)
scale = Vector2(1.13281, 1.14063)
texture = ExtResource("19_pm21f")
script = ExtResource("21_oo54l")
RegionId = 4

[node name="StaticBody2D" type="StaticBody2D" parent="RegionBlockers/Region4"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="RegionBlockers/Region4/StaticBody2D"]
scale = Vector2(12.785, 12.785)
shape = SubResource("RectangleShape2D_2lf6f")

[node name="Region5" type="Sprite2D" parent="RegionBlockers"]
position = Vector2(424, -218)
scale = Vector2(1.16406, 1.19141)
texture = ExtResource("19_pm21f")
script = ExtResource("21_oo54l")
RegionId = 5

[node name="StaticBody2D" type="StaticBody2D" parent="RegionBlockers/Region5"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="RegionBlockers/Region5/StaticBody2D"]
scale = Vector2(12.785, 12.785)
shape = SubResource("RectangleShape2D_2lf6f")

[node name="Region6" type="Sprite2D" parent="RegionBlockers"]
position = Vector2(103, -218)
scale = Vector2(1.16406, 1.19141)
texture = ExtResource("19_pm21f")
script = ExtResource("21_oo54l")
RegionId = 6

[node name="StaticBody2D" type="StaticBody2D" parent="RegionBlockers/Region6"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="RegionBlockers/Region6/StaticBody2D"]
scale = Vector2(12.785, 12.785)
shape = SubResource("RectangleShape2D_2lf6f")

[node name="Region7" type="Sprite2D" parent="RegionBlockers"]
position = Vector2(422, -544.75)
scale = Vector2(1.16406, 1.37695)
texture = ExtResource("19_pm21f")
script = ExtResource("21_oo54l")
RegionId = 7

[node name="StaticBody2D" type="StaticBody2D" parent="RegionBlockers/Region7"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="RegionBlockers/Region7/StaticBody2D"]
scale = Vector2(12.785, 12.785)
shape = SubResource("RectangleShape2D_2lf6f")

[node name="MapPanel" parent="." instance=ExtResource("28_0nbtd")]

[node name="DayNight" parent="." instance=ExtResource("32_fh13f")]
DayDurationMinutes = 6.0
NightDarknessLevel = 0.1

[node name="RegionSpecific" type="Node2D" parent="."]
y_sort_enabled = true

[node name="Region7" type="Node2D" parent="RegionSpecific"]

[node name="DungeonEntrance" parent="RegionSpecific/Region7" instance=ExtResource("41_4wdv8")]
modulate = Color(0.947199, 0.947199, 0.947199, 1)
position = Vector2(408, -600)

[node name="GoblinTerritory1" parent="RegionSpecific/Region7" instance=ExtResource("2_territory")]
position = Vector2(418, -211)
TerritoryRadius = 130.0
ShowDebugVisual = false

[node name="Region6" type="Node2D" parent="RegionSpecific"]
y_sort_enabled = true

[node name="Region6QuestSystem" parent="RegionSpecific/Region6" instance=ExtResource("41_18clu")]
position = Vector2(8, -331)

[node name="Region8" type="Node2D" parent="RegionSpecific"]
y_sort_enabled = true

[node name="EnemySpawnRegion8" parent="RegionSpecific/Region8" instance=ExtResource("49_e68xj")]
position = Vector2(105, -564)

[node name="Region5" type="Node2D" parent="RegionSpecific"]
y_sort_enabled = true

[node name="GoblinTerritory1" parent="RegionSpecific/Region5" instance=ExtResource("2_territory")]
position = Vector2(418, -211)
TerritoryRadius = 130.0
ShowDebugVisual = false

[node name="Label" type="Label" parent="RegionSpecific/Region5"]
offset_left = 46.0
offset_top = -855.0
offset_right = 87.0
offset_bottom = -832.0
scale = Vector2(3, 3)
text = "BOSS"

[node name="Label2" type="Label" parent="RegionSpecific/Region5"]
offset_left = 487.0
offset_top = -254.0
offset_right = 694.0
offset_bottom = -231.0
scale = Vector2(3, 3)
text = "2 AREA ENEMIES, NEW ORE"

[node name="Label3" type="Label" parent="RegionSpecific/Region5"]
offset_left = -48.0
offset_top = -568.0
offset_right = 115.0
offset_bottom = -545.0
scale = Vector2(3, 3)
text = "ENEMY BASE"

[node name="Label6" type="Label" parent="RegionSpecific/Region5"]
offset_left = 251.0
offset_top = -858.0
offset_right = 366.0
offset_bottom = -835.0
scale = Vector2(3, 3)
text = "STATUE (BUFF)"

[node name="Label7" type="Label" parent="RegionSpecific/Region5"]
offset_left = 424.0
offset_top = 269.0
offset_right = 657.0
offset_bottom = 292.0
scale = Vector2(3, 3)
text = "BUFF ASSIGN (FROM STATUES)"

[node name="Label4" type="Label" parent="RegionSpecific/Region5"]
offset_left = 574.0
offset_top = -632.0
offset_right = 817.0
offset_bottom = -583.0
scale = Vector2(3, 3)
text = "DUNGEON INITIALLY DIFFICULT
AREA ENEMIES"

[node name="Label5" type="Label" parent="RegionSpecific/Region5"]
offset_left = -178.0
offset_top = -270.0
offset_right = -15.0
offset_bottom = -247.0
scale = Vector2(3, 3)
text = "QUEST"

[node name="Region4" type="Node2D" parent="RegionSpecific"]
y_sort_enabled = true

[node name="Shop" parent="RegionSpecific/Region4" instance=ExtResource("28_37qwj")]
position = Vector2(127, 361)

[node name="Region3" type="Node2D" parent="RegionSpecific"]
y_sort_enabled = true

[node name="HouseExterior" parent="RegionSpecific/Region3" instance=ExtResource("43_eo1xk")]
position = Vector2(424, 408)

[node name="Region2" type="Node2D" parent="RegionSpecific"]
y_sort_enabled = true
position = Vector2(3, -16)

[node name="QuestBoard" parent="RegionSpecific/Region2" instance=ExtResource("32_rup4s")]
position = Vector2(458.68, 24.435)

[node name="Region1" type="Node2D" parent="RegionSpecific"]
y_sort_enabled = true

[node name="TutorialNpc" parent="RegionSpecific/Region1" instance=ExtResource("20_2lf6f")]
position = Vector2(200, 67)

[node name="NavigationRegion2D" type="NavigationRegion2D" parent="."]
position = Vector2(-186, 55)
navigation_polygon = SubResource("NavigationPolygon_5hglh")
